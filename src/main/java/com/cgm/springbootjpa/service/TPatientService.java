package com.cgm.springbootjpa.service;

import com.cgm.springbootjpa.dto.PatientDto;
import com.cgm.springbootjpa.entity.TPatient;
import com.cgm.springbootjpa.mapper.PatientMapper;
import com.cgm.springbootjpa.repository.TPatientRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (TPatient)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-10 10:48:39
 */
@Service("tPatientService")
public class TPatientService {

    @Autowired
    private TPatientRepository tPatientRepo;

    @Autowired
    private PatientMapper patientMapper;


    public PatientDto queryById(Long id) {
        TPatient patient = this.tPatientRepo.findById(id).orElse(null);
        return patientMapper.toDto(patient);
    }


    public List<PatientDto> getAllInfo() {
        List<TPatient> patients = this.tPatientRepo.findAll();
        return patientMapper.toDtoList(patients);
    }


    public Page<TPatient> queryAllByLimit(int offset, int limit) {
        return this.tPatientRepo.findAll(PageRequest.of((offset - 1) * limit, limit));
    }


    public PatientDto insert(PatientDto patientDto) {
        TPatient patient = patientMapper.toEntity(patientDto);
        TPatient savedPatient = this.tPatientRepo.save(patient);
        return patientMapper.toDto(savedPatient);
    }


    public PatientDto update(PatientDto patientDto) {
        TPatient patient = patientMapper.toEntity(patientDto);
        TPatient updatedPatient = this.tPatientRepo.save(patient);
        return patientMapper.toDto(updatedPatient);
    }


    public boolean deleteById(Long id) {

        try {
            this.tPatientRepo.deleteById(id);
        } catch (Exception ex) {
            return false;
        }
        return true;

    }
}

