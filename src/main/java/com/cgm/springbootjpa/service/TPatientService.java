package com.cgm.springbootjpa.service;

import com.cgm.springbootjpa.entity.TPatient;
import com.cgm.springbootjpa.repository.TPatientRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (TPatient)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-10 10:48:39
 */
@Service("tPatientService")
public class TPatientService {

    @Autowired
    private TPatientRepository tPatientRepo;


    public TPatient queryById(Long id) {
        return this.tPatientRepo.getById(id);
    }


    public List<TPatient> getAllInfo() {
        return this.tPatientRepo.findAll();

    }


    public Page<TPatient> queryAllByLimit(int offset, int limit) {
        return this.tPatientRepo.findAll(PageRequest.of((offset - 1) * limit, limit));
    }


    public TPatient insert(TPatient tPatient) {

        return this.tPatientRepo.save(tPatient);
    }


    public TPatient update(TPatient tPatient) {

        return this.tPatientRepo.save(tPatient);
    }


    public boolean deleteById(Long id) {

        try {
            this.tPatientRepo.deleteById(id);
        } catch (Exception ex) {
            return false;
        }
        return true;

    }
}

