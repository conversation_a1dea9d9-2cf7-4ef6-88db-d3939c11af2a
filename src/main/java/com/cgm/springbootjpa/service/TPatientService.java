package com.cgm.springbootjpa.service;

import com.cgm.springbootjpa.dto.CreatePatientDto;
import com.cgm.springbootjpa.dto.PatientDto;
import com.cgm.springbootjpa.entity.TPatient;
import com.cgm.springbootjpa.entity.TPatientGroup;
import com.cgm.springbootjpa.mapper.PatientMapper;
import com.cgm.springbootjpa.repository.TPatientRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;

/**
 * (TPatient)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-10 10:48:39
 */
@Service("tPatientService")
public class TPatientService {

    @Autowired
    private TPatientRepository tPatientRepo;

    @Autowired
    private PatientMapper patientMapper;


    public PatientDto queryById(Long id) {
        TPatient patient = this.tPatientRepo.findById(id).orElse(null);
        if (patient == null) {
            return null;
        }
        return patientMapper.toDto(patient);
    }


    public List<PatientDto> getAllInfo() {
        List<TPatient> patients = this.tPatientRepo.findAll();
        return patientMapper.toDtoList(patients);
    }


    public Page<TPatient> queryAllByLimit(int offset, int limit) {
        return this.tPatientRepo.findAll(PageRequest.of((offset - 1) * limit, limit));
    }


    public PatientDto insert(CreatePatientDto createPatientDto) {
        // 使用CreatePatientDto映射到实体
        TPatient patient = patientMapper.toEntity(createPatientDto);

        // 设置创建和更新时间
        Instant now = Instant.now();
        patient.setCreateTime(now);
        patient.setUpdateTime(now);

        // 如果有患者组ID，需要设置关联关系
        if (createPatientDto.getPatientGroupId() != null) {
            TPatientGroup patientGroup = new TPatientGroup();
            patientGroup.setId(createPatientDto.getPatientGroupId());
            patient.setPatientGroup(patientGroup);
        }

        // 直接保存，不需要先查询
        TPatient savedPatient = this.tPatientRepo.save(patient);
        return patientMapper.toDto(savedPatient);
    }


    public PatientDto update(PatientDto patientDto) {
        // 更新时需要先检查记录是否存在
        if (patientDto.getId() == null || !this.tPatientRepo.existsById(patientDto.getId())) {
            throw new RuntimeException("Patient not found with id: " + patientDto.getId());
        }

        TPatient patient = patientMapper.toEntity(patientDto);

        // 设置更新时间
        patient.setUpdateTime(Instant.now());

        // 如果有患者组ID，需要设置关联关系
        if (patientDto.getPatientGroupId() != null) {
            TPatientGroup patientGroup = new TPatientGroup();
            patientGroup.setId(patientDto.getPatientGroupId());
            patient.setPatientGroup(patientGroup);
        }

        TPatient updatedPatient = this.tPatientRepo.save(patient);
        return patientMapper.toDto(updatedPatient);
    }


    public boolean deleteById(Long id) {

        try {
            this.tPatientRepo.deleteById(id);
        } catch (Exception ex) {
            return false;
        }
        return true;

    }
}

