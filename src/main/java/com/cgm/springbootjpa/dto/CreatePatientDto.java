package com.cgm.springbootjpa.dto;

import lombok.Data;

import java.time.Instant;
import java.time.LocalDate;

/**
 * 创建患者DTO（用于新增患者时的请求参数）
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Data
public class CreatePatientDto {
    
    private Long age;
    
    private LocalDate birthday;
    
    private Byte gender;
    
    private String idCard;
    
    private String name;
    
    private Long orgId;
    
    private String orgName;
    
    private String pyName;
    
    private String remark;
    
    private Byte status;
    
    /**
     * 患者组ID
     */
    private Long patientGroupId;
}
