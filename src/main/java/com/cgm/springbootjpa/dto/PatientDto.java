package com.cgm.springbootjpa.dto;

import com.cgm.springbootjpa.core.valid.Add;
import com.cgm.springbootjpa.core.valid.Update;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDate;

/**
 * 患者DTO
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Data
public class PatientDto {

    @Null(groups = Add.class, message = "ID必须为空")
    @NotNull(groups = Update.class, message = "ID不能为空")
    private Long id;

    private Instant createTime;

    private Instant updateTime;
    
    private Long age;

    private LocalDate birthday;
    
    private Byte gender;
    
    private String idCard;
    
    private String name;
    
    private Long orgId;
    
    private String orgName;
    
    private String pyName;
    
    private String remark;
    
    private Byte status;

    /**
     * 患者组ID
     */
    private Long patientGroupId;

    /**
     * 患者组信息（仅用于查询返回）
     */
    private PatientGroupDto patientGroup;
}
