package com.cgm.springbootjpa.easycode.service.impl;

import com.cgm.springbootjpa.easycode.domain.entity.TPatient;
import com.cgm.springbootjpa.easycode.domain.repo.TPatientRepo;
import com.cgm.springbootjpa.easycode.service.TPatientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.PageRequest;

import java.util.List;

import org.springframework.data.domain.Page;

/**
 * (TPatient)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-10 10:48:39
 */
@Service("tPatientService")
public class TPatientServiceImpl implements TPatientService {

    @Autowired
    private TPatientRepo tPatientRepo;

    @Override
    public TPatient queryById(Long id) {
        return this.tPatientRepo.getById(id);
    }

    @Override
    public List<TPatient> getAllInfo() {
        return this.tPatientRepo.findAll();

    }

    @Override
    public Page<TPatient> queryAllByLimit(int offset, int limit) {
        return this.tPatientRepo.findAll(PageRequest.of((offset - 1) * limit, limit));
    }

    @Override
    public TPatient insert(TPatient tPatient) {

        return this.tPatientRepo.save(tPatient);
    }


    @Override
    public TPatient update(TPatient tPatient) {

        return this.tPatientRepo.save(tPatient);
    }


    @Override
    public boolean deleteById(Long id) {

        try {
            this.tPatientRepo.deleteById(id);
        } catch (Exception ex) {
            return false;
        }
        return true;

    }
}

