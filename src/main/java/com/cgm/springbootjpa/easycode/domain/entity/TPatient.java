package com.cgm.springbootjpa.easycode.domain.entity;

import java.util.Date;
import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;


/**
 * (TPatient)实体类
 *
 * <AUTHOR>
 * @since 2025-09-10 10:48:40
 */
@Entity
@Table(name = "t_patient")
public class TPatient implements Serializable {
    private static final long serialVersionUID = 828021278570453366L;
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id", nullable = false)
    private Long id;
    /**
     * 创建时间
     */
    @Column(name = "createTime", nullable = false)
    private Date createTime;
    /**
     * 更新时间
     */
    @Column(name = "updateTime", nullable = false)
    private Date updateTime;
    /**
     * 年龄
     */
    @Column(name = "age", nullable = false)
    private Long age;
    /**
     * 生日
     */
    @Column(name = "birthday", nullable = false)
    private Date birthday;
    /**
     * 性别
     */
    @Column(name = "gender", nullable = false)
    private Integer gender;
    /**
     * 证件编号
     */
    @Column(name = "idCard", nullable = false)
    private String idCard;
    /**
     * 名字
     */
    @Column(name = "name", nullable = false)
    private String name;
    /**
     * 医院Id
     */
    @Column(name = "orgId", nullable = false)
    private Long orgId;
    /**
     * 医院名称
     */
    @Column(name = "orgName", nullable = false)
    private String orgName;
    /**
     * 拼音名称
     */
    @Column(name = "pyName", nullable = false)
    private String pyName;
    /**
     * 备注
     */
    @Column(name = "remark", nullable = false)
    private String remark;
    /**
     * 状态
     */
    @Column(name = "status", nullable = false)
    private Integer status;
    @Column(name = "patientGroupId", nullable = false)
    private Long patientGroupId;



    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getAge() {
        return age;
    }

    public void setAge(Long age) {
        this.age = age;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getPyName() {
        return pyName;
    }

    public void setPyName(String pyName) {
        this.pyName = pyName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getPatientGroupId() {
        return patientGroupId;
    }

    public void setPatientGroupId(Long patientGroupId) {
        this.patientGroupId = patientGroupId;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
}

