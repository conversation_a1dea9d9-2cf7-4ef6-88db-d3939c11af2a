package com.cgm.springbootjpa.mapper;

import com.cgm.springbootjpa.dto.PatientDto;
import com.cgm.springbootjpa.dto.PatientGroupDto;
import com.cgm.springbootjpa.entity.TPatient;
import com.cgm.springbootjpa.entity.TPatientGroup;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 患者实体与DTO之间的映射器
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Mapper(componentModel = "spring")
public interface PatientMapper {
    
    PatientMapper INSTANCE = Mappers.getMapper(PatientMapper.class);
    
    /**
     * 实体转DTO
     *
     * @param patient 患者实体
     * @return 患者DTO
     */
    PatientDto toDto(TPatient patient);
    
    /**
     * DTO转实体
     *
     * @param patientDto 患者DTO
     * @return 患者实体
     */
    TPatient toEntity(PatientDto patientDto);
    
    /**
     * 实体列表转DTO列表
     *
     * @param patients 患者实体列表
     * @return 患者DTO列表
     */
    List<PatientDto> toDtoList(List<TPatient> patients);
    
    /**
     * 患者组实体转DTO
     *
     * @param patientGroup 患者组实体
     * @return 患者组DTO
     */
    PatientGroupDto toDto(TPatientGroup patientGroup);
    
    /**
     * 患者组DTO转实体
     *
     * @param patientGroupDto 患者组DTO
     * @return 患者组实体
     */
    TPatientGroup toEntity(PatientGroupDto patientGroupDto);
}
