package com.cgm.springbootjpa.controller;

import com.cgm.springbootjpa.entity.TPatient;
import com.cgm.springbootjpa.service.TPatientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * (TPatient)表控制层
 *
 * <AUTHOR>
 * @since 2025-09-10 10:48:25
 */
@RestController
@RequestMapping("tPatient")
public class TPatientController {
    /**
     * 服务对象
     */
    @Autowired
    private TPatientService tPatientService;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("selectOne/{id}")
     public ResponseEntity<TPatient> selectOne(@PathVariable(value = "id")  Long id) {
        return ResponseEntity.ok(this.tPatientService.queryById(id));
    }

}

