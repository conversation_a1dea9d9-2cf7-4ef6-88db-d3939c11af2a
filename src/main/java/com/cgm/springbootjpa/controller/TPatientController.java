package com.cgm.springbootjpa.controller;

import com.cgm.springbootjpa.dto.PatientDto;
import com.cgm.springbootjpa.service.TPatientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * (TPatient)表控制层
 *
 * <AUTHOR>
 * @since 2025-09-10 10:48:25
 */
@RestController
@RequestMapping("tPatient")
@Tag(name = "患者管理")
public class TPatientController {
    /**
     * 服务对象
     */
    @Autowired
    private TPatientService tPatientService;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @Operation(summary = "根据患者id查询患者信息")
    @GetMapping("selectOne/{id}")
    public ResponseEntity<PatientDto> selectOne(@PathVariable(value = "id") Long id) {
        PatientDto patientDto = this.tPatientService.queryById(id);
        return ResponseEntity.ok(patientDto);
    }

    /**
     * 查询所有患者数据
     *
     * @return 患者列表
     */
    @Operation(summary = "分页查询患者列表")
    @GetMapping("list")
    public ResponseEntity<List<PatientDto>> getAllPatients() {
        List<PatientDto> patients = this.tPatientService.getAllInfo();
        return ResponseEntity.ok(patients);
    }

    /**
     * 新增患者
     *
     * @param patientDto 患者信息
     * @return 新增后的患者信息
     */
    @Operation(summary = "添加患者信息")
    @Parameters({
            @Parameter(name = "PatientDto",description = "患者信息",in = ParameterIn.DEFAULT),
    })
    @PostMapping("add")
    public ResponseEntity<PatientDto> addPatient(@RequestBody @Validated PatientDto patientDto) {
        PatientDto savedPatient = this.tPatientService.insert(patientDto);
        return ResponseEntity.ok(savedPatient);
    }

    /**
     * 更新患者信息
     *
     * @param patientDto 患者信息
     * @return 更新后的患者信息
     */
    @Operation(summary = "更新患者信息")
    @PutMapping("update")
    public ResponseEntity<PatientDto> updatePatient(@RequestBody PatientDto patientDto) {
        PatientDto updatedPatient = this.tPatientService.update(patientDto);
        return ResponseEntity.ok(updatedPatient);
    }

    /**
     * 删除患者
     *
     * @param id 患者ID
     * @return 删除结果
     */
    @Operation(summary = "删除患者信息")
    @DeleteMapping("delete/{id}")
    public ResponseEntity<Boolean> deletePatient(@PathVariable Long id) {
        boolean result = this.tPatientService.deleteById(id);
        return ResponseEntity.ok(result);
    }

}

