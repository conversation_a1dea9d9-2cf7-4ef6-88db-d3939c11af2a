package com.cgm.springbootjpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "t_patient_group")
public class TPatientGroup {
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "create_time", nullable = false)
    private Instant createTime;

    @Column(name = "update_time", nullable = false)
    private Instant updateTime;

    @Column(name = "bids_version")
    private String bidsVersion;

    @Column(name = "description", length = 2048)
    private String description;

    @Column(name = "name", nullable = false, length = 1024)
    private String name;

    @Column(name = "org_id")
    private Long orgId;

}