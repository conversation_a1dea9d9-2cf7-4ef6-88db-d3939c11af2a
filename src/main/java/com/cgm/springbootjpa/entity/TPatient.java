package com.cgm.springbootjpa.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(name = "t_patient")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class TPatient {
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "create_time", nullable = false)
    private Instant createTime;

    @Column(name = "update_time", nullable = false)
    private Instant updateTime;

    @Column(name = "age")
    private Long age;

    @Column(name = "birthday")
    private Instant birthday;

    @Column(name = "gender", nullable = false)
    private Byte gender;

    @Column(name = "id_card", nullable = false)
    private String idCard;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "org_id", nullable = false)
    private Long orgId;

    @Column(name = "org_name", length = 1024)
    private String orgName;

    @Column(name = "py_name")
    private String pyName;

    @Column(name = "remark", length = 2048)
    private String remark;

    @Column(name = "status")
    private Byte status;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "patient_group_id")
    private TPatientGroup patientGroup;

}