package entity.service;

import entity.domain.entity.TPatient;

import java.util.List;

import org.springframework.data.domain.Page;

/**
 * (TPatient)表服务接口
 *
 * <AUTHOR>
 * @since 2025-09-10 10:48:36
 */
public interface TPatientService {
    TPatient queryById(Long id);

    Page<TPatient> queryAllByLimit(int offset, int limit);

    TPatient insert(TPatient tPatient);

    TPatient update(TPatient tPatient);

    boolean deleteById(Long id);

    List<TPatient> getAllInfo();
}

